# Java Basics

## Topics Covered
1. Variables and Data Types
2. Operators
3. Input/Output
4. Type Casting

## Key Concepts

### Variables and Data Types
```java
// Primitive Data Types
int age = 25;
double salary = 50000.50;
char grade = 'A';
boolean isActive = true;
byte smallNumber = 127;
short mediumNumber = 32000;
long bigNumber = 1234567890L;
float percentage = 85.5f;
```

### Operators
- **Arithmetic**: +, -, *, /, %
- **Relational**: ==, !=, <, >, <=, >=
- **Logical**: &&, ||, !
- **Assignment**: =, +=, -=, *=, /=
- **Increment/Decrement**: ++, --

### Input/Output
```java
Scanner scanner = new Scanner(System.in);
System.out.println("Enter your name: ");
String name = scanner.nextLine();
System.out.println("Hello, " + name);
```

## Practice Files
- `DataTypesExample.java` - Basic data types demonstration
- `OperatorsExample.java` - All operators with examples
- `InputOutputExample.java` - Scanner usage
- `TypeCastingExample.java` - Implicit and explicit casting

## Exercises
1. Create a program to calculate simple interest
2. Write a program to swap two numbers
3. Create a basic calculator for two numbers
4. Convert temperature from Celsius to Fahrenheit

## Daily Challenge
Write a program that takes user input for:
- Name, Age, Salary
- Calculate and display annual salary
- Determine if person is eligible for loan (age > 18 && salary > 30000)
