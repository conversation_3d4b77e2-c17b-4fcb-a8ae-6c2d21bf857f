# Day 1 - Level 1 (<PERSON><PERSON>ner)

## Level 1 Overview
This is the beginner level for Day 1. Start here if you're completely new to Java programming.

## Learning Objectives
- Write your first Java program
- Understand basic syntax
- Learn about variables and simple data types
- Practice with basic output statements

## Topics for Level 1

### 1. Your First Java Program
- Understanding the basic structure of a Java program
- The `main` method
- Using `System.out.println()`

### 2. Basic Variables
- Declaring variables
- Common data types: `int`, `double`, `String`, `boolean`
- Assigning values to variables

### 3. Simple Output
- Printing text to console
- Printing variables
- Basic string concatenation

## Practice Files
- `FirstProgram.java` - Your very first Java program
- `BasicVariables.java` - Simple variable practice
- `SimpleOutput.java` - Different ways to print output

## Level 1 Exercises (Easy)
1. **Hello Your Name**: Modify Hello World to print your name
2. **Age Calculator**: Create variables for birth year and current year, calculate age
3. **Favorite Things**: Create variables for your favorite color, food, and number, then print them
4. **Simple Math**: Create two numbers and print their sum

## Success Criteria
✅ Can write a basic Java program that compiles and runs  
✅ Can declare and use variables  
✅ Can print output to console  
✅ Understands basic Java syntax  

## Next Steps
Once you complete Level 1:
- Move to `level2` for intermediate concepts
- Or continue with more Level 1 practice if needed

## Tips for Level 1
- Don't worry about understanding everything perfectly
- Focus on getting programs to compile and run
- Practice typing the code yourself (don't just copy-paste)
- Experiment with changing values and see what happens
