# Day 1 - Java Fundamentals

## Today's Learning Goals
- Understand Java basics and setup
- Learn about variables and data types
- Practice with operators
- Write your first Java programs

## Topics to Cover

### 1. Java Environment Setup
- JDK installation verification
- Understanding Java compilation and execution
- Writing your first "Hello World" program

### 2. Variables and Data Types
- Primitive data types (int, double, char, boolean, etc.)
- Variable declaration and initialization
- Understanding memory allocation

### 3. Operators
- Arithmetic operators (+, -, *, /, %)
- Relational operators (==, !=, <, >, <=, >=)
- Logical operators (&&, ||, !)
- Assignment operators (=, +=, -=, etc.)

## Practice Files for Today
- `HelloWorld.java` - Your first Java program
- `VariablesDemo.java` - Variables and data types practice
- `OperatorsDemo.java` - All operators with examples
- `SimpleCalculator.java` - Basic calculator program

## Exercises for Day 1
1. **Hello World Variations**: Create different versions of Hello World
2. **Variable Practice**: Declare variables of all primitive types
3. **Basic Calculator**: Create a simple calculator for two numbers
4. **Temperature Converter**: Convert Celsius to Fahrenheit
5. **Simple Interest Calculator**: Calculate simple interest

## Daily Challenge
Create a program that:
1. Takes user input for name, age, and salary
2. Calculates annual salary
3. Determines eligibility for a loan (age >= 18 && salary >= 30000)
4. Displays a formatted output with all information

## Notes Section
Write your observations and learnings here:
- 
- 
- 

## Tomorrow's Preview
Day 2 will cover:
- Control Flow Statements (if-else, switch)
- Loops (for, while, do-while)
- Method basics
