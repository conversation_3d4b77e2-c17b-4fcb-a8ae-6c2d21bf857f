/**
 * Data Types Example - Demonstrates all primitive data types in Java
 * Author: Daily Practice
 * Date: Today
 */

public class DataTypesExample {
    public static void main(String[] args) {
        // Integer types
        byte byteVar = 127;                    // 8-bit, range: -128 to 127
        short shortVar = 32000;                // 16-bit, range: -32,768 to 32,767
        int intVar = 2147483647;               // 32-bit, range: -2^31 to 2^31-1
        long longVar = 9223372036854775807L;   // 64-bit, range: -2^63 to 2^63-1
        
        // Floating point types
        float floatVar = 3.14159f;             // 32-bit IEEE 754
        double doubleVar = 3.141592653589793;  // 64-bit IEEE 754
        
        // Character type
        char charVar = 'A';                    // 16-bit Unicode character
        
        // Boolean type
        boolean booleanVar = true;             // true or false
        
        // Display all variables
        System.out.println("=== Java Primitive Data Types ===");
        System.out.println("byte value: " + byteVar + " (Size: 1 byte)");
        System.out.println("short value: " + shortVar + " (Size: 2 bytes)");
        System.out.println("int value: " + intVar + " (Size: 4 bytes)");
        System.out.println("long value: " + longVar + " (Size: 8 bytes)");
        System.out.println("float value: " + floatVar + " (Size: 4 bytes)");
        System.out.println("double value: " + doubleVar + " (Size: 8 bytes)");
        System.out.println("char value: " + charVar + " (Size: 2 bytes)");
        System.out.println("boolean value: " + booleanVar + " (Size: 1 bit)");
        
        // String (Reference type)
        String stringVar = "Hello, Java!";
        System.out.println("String value: " + stringVar + " (Reference type)");
        
        // Demonstrate ranges
        System.out.println("\n=== Data Type Ranges ===");
        System.out.println("Byte range: " + Byte.MIN_VALUE + " to " + Byte.MAX_VALUE);
        System.out.println("Short range: " + Short.MIN_VALUE + " to " + Short.MAX_VALUE);
        System.out.println("Int range: " + Integer.MIN_VALUE + " to " + Integer.MAX_VALUE);
        System.out.println("Long range: " + Long.MIN_VALUE + " to " + Long.MAX_VALUE);
        System.out.println("Float range: " + Float.MIN_VALUE + " to " + Float.MAX_VALUE);
        System.out.println("Double range: " + Double.MIN_VALUE + " to " + Double.MAX_VALUE);
    }
}
