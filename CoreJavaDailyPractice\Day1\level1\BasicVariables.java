/**
 * Day 1 - Level 1 - Basic Variables Practice
 * 
 * Learn how to create and use variables in Java
 * This program shows the most common variable types
 */

public class BasicVariables {
    public static void main(String[] args) {
        System.out.println("=== Learning About Variables ===");
        
        // Integer variable (whole numbers)
        int myAge = 25;
        System.out.println("My age is: " + myAge);
        
        // String variable (text)
        String myName = "Alex";
        System.out.println("My name is: " + myName);
        
        // Double variable (decimal numbers)
        double myHeight = 5.8;
        System.out.println("My height is: " + myHeight + " feet");
        
        // Boolean variable (true or false)
        boolean iLikeJava = true;
        System.out.println("Do I like Java? " + iLikeJava);
        
        System.out.println("\n=== Let's Do Some Simple Math ===");
        
        // Create two numbers
        int firstNumber = 10;
        int secondNumber = 5;
        
        // Add them together
        int sum = firstNumber + secondNumber;
        
        System.out.println("First number: " + firstNumber);
        System.out.println("Second number: " + secondNumber);
        System.out.println("Sum: " + sum);
        
        System.out.println("\n=== More Variable Examples ===");
        
        // Your favorite things
        String favoriteColor = "Blue";
        String favoriteFood = "Pizza";
        int favoriteNumber = 7;
        
        System.out.println("My favorite color is: " + favoriteColor);
        System.out.println("My favorite food is: " + favoriteFood);
        System.out.println("My favorite number is: " + favoriteNumber);
        
        // Simple calculation
        int birthYear = 1998;
        int currentYear = 2024;
        int calculatedAge = currentYear - birthYear;
        
        System.out.println("\nAge Calculation:");
        System.out.println("Birth year: " + birthYear);
        System.out.println("Current year: " + currentYear);
        System.out.println("Calculated age: " + calculatedAge);
        
        System.out.println("\n🎉 Great job learning about variables!");
    }
}

/*
 * What you learned:
 * 1. int - for whole numbers (like age, count)
 * 2. String - for text (like names, words)
 * 3. double - for decimal numbers (like height, price)
 * 4. boolean - for true/false values
 * 5. How to do simple math with variables
 * 6. How to print variables with text
 * 
 * Try this:
 * - Change the values of the variables
 * - Add your own variables
 * - Try different calculations
 */
