/**
 * Day 1 - Hello World Program
 * This is your first Java program!
 * 
 * Learning Objectives:
 * - Understand basic Java program structure
 * - Learn about main method
 * - Practice with System.out.println()
 */

public class HelloWorld {
    // Main method - entry point of Java program
    public static void main(String[] args) {
        // Print Hello World to console
        System.out.println("Hello, World!");
        
        // Different ways to print
        System.out.println("Welcome to Java Programming!");
        System.out.print("This is print without new line. ");
        System.out.println("This continues on the same line.");
        
        // Using printf for formatted output
        System.out.printf("Today is Day %d of Java learning!%n", 1);
        
        // Printing multiple lines
        System.out.println("=================================");
        System.out.println("    JAVA DAILY PRACTICE - DAY 1  ");
        System.out.println("=================================");
        System.out.println("Topics covered today:");
        System.out.println("1. Hello World Program");
        System.out.println("2. Variables and Data Types");
        System.out.println("3. Operators");
        System.out.println("4. Basic Input/Output");
        
        // Fun with escape sequences
        System.out.println("\nEscape Sequences Demo:");
        System.out.println("Tab character:\tHello");
        System.out.println("New line character:\nHello");
        System.out.println("Backslash: \\");
        System.out.println("Double quote: \"Hello\"");
        System.out.println("Single quote: \'Hello\'");
        
        // ASCII Art (optional fun)
        System.out.println("\n   ☕ Happy Java Coding! ☕");
    }
}

/*
 * How to compile and run:
 * 1. Open terminal/command prompt
 * 2. Navigate to the file directory
 * 3. Compile: javac HelloWorld.java
 * 4. Run: java HelloWorld
 * 
 * Expected Output:
 * Hello, World!
 * Welcome to Java Programming!
 * ... (and more output as shown above)
 */
