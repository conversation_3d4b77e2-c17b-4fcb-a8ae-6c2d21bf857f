# Control Flow in Java

## Topics Covered
1. Conditional Statements (if, if-else, nested if, switch)
2. Loops (for, while, do-while, enhanced for)
3. Jump Statements (break, continue, return)

## Key Concepts

### Conditional Statements

#### if-else Statement
```java
if (condition) {
    // code block
} else if (anotherCondition) {
    // code block
} else {
    // code block
}
```

#### Switch Statement
```java
switch (variable) {
    case value1:
        // code
        break;
    case value2:
        // code
        break;
    default:
        // default code
}
```

### Loops

#### For Loop
```java
for (initialization; condition; increment/decrement) {
    // code block
}
```

#### While Loop
```java
while (condition) {
    // code block
}
```

#### Do-While Loop
```java
do {
    // code block
} while (condition);
```

#### Enhanced For Loop (For-Each)
```java
for (dataType variable : array/collection) {
    // code block
}
```

### Jump Statements
- **break**: Exit from loop or switch
- **continue**: Skip current iteration
- **return**: Exit from method

## Practice Files
- `ConditionalExample.java` - If-else and switch examples
- `LoopsExample.java` - All types of loops
- `JumpStatementsExample.java` - Break, continue, return
- `NestedLoopsExample.java` - Nested loop patterns

## Exercises
1. Write a program to check if a number is positive, negative, or zero
2. Create a simple calculator using switch statement
3. Print multiplication table using loops
4. Find factorial of a number using different loop types
5. Print various patterns using nested loops

## Daily Challenge
Create a menu-driven program that:
1. Displays a menu with options
2. Uses switch to handle user choice
3. Implements each option with appropriate loops
4. Continues until user chooses to exit
