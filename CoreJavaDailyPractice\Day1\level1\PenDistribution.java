/**
 * Level 1 Practice Program 5
 * Problem: Suppose you have to divide 14 pens among 3 students equally. 
 *          Write a program to find how many pens each student will get if the pens 
 *          must be divided equally. Also, find the remaining non-distributed pens.
 * Hint: 
 * - Use Modulus Operator (%) to find the remainder.
 * - Use Division Operator to find the Quantity of pens
 * I/P => NONE
 * O/P => The Pen Per Student is ___ and the remaining pen not distributed is ___
 */

public class PenDistribution {
    public static void main(String[] args) {
        // Given values
        int totalPens = 14;
        int numberOfStudents = 3;
        
        // Calculate pens per student using division operator
        int pensPerStudent = totalPens / numberOfStudents;
        
        // Calculate remaining pens using modulus operator
        int remainingPens = totalPens % numberOfStudents;
        
        // Display the result
        System.out.println("The Pen Per Student is " + pensPerStudent + " and the remaining pen not distributed is " + remainingPens);
        
        // Optional: Show detailed breakdown
        System.out.println("\nDetailed Breakdown:");
        System.out.println("Total Pens: " + totalPens);
        System.out.println("Number of Students: " + numberOfStudents);
        System.out.println("Pens per Student: " + totalPens + " ÷ " + numberOfStudents + " = " + pensPerStudent);
        System.out.println("Remaining Pens: " + totalPens + " % " + numberOfStudents + " = " + remainingPens);
        
        // Verification
        System.out.println("\nVerification:");
        System.out.println("Distributed Pens: " + numberOfStudents + " × " + pensPerStudent + " = " + (numberOfStudents * pensPerStudent));
        System.out.println("Remaining Pens: " + remainingPens);
        System.out.println("Total: " + (numberOfStudents * pensPerStudent) + " + " + remainingPens + " = " + ((numberOfStudents * pensPerStudent) + remainingPens));
    }
}

/*
 * Expected Output:
 * The Pen Per Student is 4 and the remaining pen not distributed is 2
 * 
 * Detailed Breakdown:
 * Total Pens: 14
 * Number of Students: 3
 * Pens per Student: 14 ÷ 3 = 4
 * Remaining Pens: 14 % 3 = 2
 * 
 * Verification:
 * Distributed Pens: 3 × 4 = 12
 * Remaining Pens: 2
 * Total: 12 + 2 = 14
 * 
 * Explanation:
 * - Division operator (/) gives the quotient: 14 ÷ 3 = 4 (each student gets 4 pens)
 * - Modulus operator (%) gives the remainder: 14 % 3 = 2 (2 pens left over)
 * - Verification: 3 students × 4 pens + 2 remaining = 12 + 2 = 14 total pens ✓
 */
