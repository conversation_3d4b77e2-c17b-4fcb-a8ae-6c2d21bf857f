/**
 * Level 1 Practice Program 2
 * Problem: <PERSON>'s mark in Maths is 94, Physics is 95 and Chemistry is 96 out of 100. 
 *          Find the average percent mark in PCM
 * I/P => NONE
 * O/P => <PERSON>'s average mark in PCM is ___
 */

public class SamMarks {
    public static void main(String[] args) {
        // <PERSON>'s marks in each subject (out of 100)
        int mathsMarks = 94;
        int physicsMarks = 95;
        int chemistryMarks = 96;
        
        // Calculate total marks
        int totalMarks = mathsMarks + physicsMarks + chemistryMarks;
        
        // Calculate average marks
        // Since we have 3 subjects, we divide by 3
        double averageMarks = totalMarks / 3.0; // Using 3.0 to get decimal result
        
        // Display the result
        System.out.println("Sam's average mark in PCM is " + averageMarks);
        
        // Optional: Show the calculation breakdown
        System.out.println("\nBreakdown:");
        System.out.println("Maths: " + mathsMarks);
        System.out.println("Physics: " + physicsMarks);
        System.out.println("Chemistry: " + chemistryMarks);
        System.out.println("Total: " + totalMarks);
        System.out.println("Average: " + totalMarks + " ÷ 3 = " + averageMarks);
    }
}

/*
 * Expected Output:
 * Sam's average mark in PCM is 95.0
 * 
 * Breakdown:
 * Maths: 94
 * Physics: 95
 * Chemistry: 96
 * Total: 285
 * Average: 285 ÷ 3 = 95.0
 * 
 * Explanation:
 * - PCM stands for Physics, Chemistry, Mathematics
 * - Total marks = 94 + 95 + 96 = 285
 * - Average = Total marks ÷ Number of subjects
 * - Average = 285 ÷ 3 = 95.0%
 */
