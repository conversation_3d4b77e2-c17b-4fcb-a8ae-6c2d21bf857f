/**
 * Level 1 Practice Program 1
 * Problem: Find the age of Harry if the birth year is 2000. Assume the Current Year is 2024
 * I/P => NONE
 * O/P => Harry's age in 2024 is ___
 */

public class HarryAge {
    public static void main(String[] args) {
        // Given information
        int birthYear = 2000;
        int currentYear = 2024;
        
        // Calculate <PERSON>'s age
        int harryAge = currentYear - birthYear;
        
        // Display the result
        System.out.println("Harry's age in 2024 is " + harryAge);
    }
}

/*
 * Expected Output:
 * Harry's age in 2024 is 24
 * 
 * Explanation:
 * - Harry was born in 2000
 * - Current year is 2024
 * - Age = Current Year - Birth Year
 * - Age = 2024 - 2000 = 24 years
 */
