/**
 * Day 1 - Variables and Data Types Demo
 * 
 * Learning Objectives:
 * - Understand all primitive data types
 * - Learn variable declaration and initialization
 * - Practice with different data types
 */

public class VariablesDemo {
    public static void main(String[] args) {
        System.out.println("=== JAVA PRIMITIVE DATA TYPES ===\n");
        
        // Integer Types
        System.out.println("INTEGER TYPES:");
        byte byteVar = 100;                    // 8-bit: -128 to 127
        short shortVar = 1000;                 // 16-bit: -32,768 to 32,767
        int intVar = 100000;                   // 32-bit: -2^31 to 2^31-1
        long longVar = 10000000000L;           // 64-bit: -2^63 to 2^63-1 (note the 'L')
        
        System.out.println("byte: " + byteVar + " (Range: " + Byte.MIN_VALUE + " to " + Byte.MAX_VALUE + ")");
        System.out.println("short: " + shortVar + " (Range: " + Short.MIN_VALUE + " to " + Short.MAX_VALUE + ")");
        System.out.println("int: " + intVar + " (Range: " + Integer.MIN_VALUE + " to " + Integer.MAX_VALUE + ")");
        System.out.println("long: " + longVar + " (Range: " + Long.MIN_VALUE + " to " + Long.MAX_VALUE + ")");
        
        // Floating Point Types
        System.out.println("\nFLOATING POINT TYPES:");
        float floatVar = 3.14159f;             // 32-bit (note the 'f')
        double doubleVar = 3.141592653589793;  // 64-bit (default for decimals)
        
        System.out.println("float: " + floatVar + " (Precision: ~7 decimal digits)");
        System.out.println("double: " + doubleVar + " (Precision: ~15 decimal digits)");
        
        // Character Type
        System.out.println("\nCHARACTER TYPE:");
        char charVar1 = 'A';                   // Single character in single quotes
        char charVar2 = 65;                    // ASCII value (A = 65)
        char charVar3 = '\u0041';              // Unicode (A = U+0041)
        
        System.out.println("char (letter): " + charVar1);
        System.out.println("char (ASCII): " + charVar2);
        System.out.println("char (Unicode): " + charVar3);
        
        // Boolean Type
        System.out.println("\nBOOLEAN TYPE:");
        boolean isJavaFun = true;
        boolean isLearningHard = false;
        
        System.out.println("Is Java fun? " + isJavaFun);
        System.out.println("Is learning hard? " + isLearningHard);
        
        // String (Reference Type - not primitive)
        System.out.println("\nSTRING (Reference Type):");
        String name = "Java Learner";
        String message = "Welcome to Day 1!";
        
        System.out.println("Name: " + name);
        System.out.println("Message: " + message);
        
        // Variable Operations
        System.out.println("\n=== VARIABLE OPERATIONS ===");
        
        // Arithmetic with variables
        int num1 = 10;
        int num2 = 3;
        System.out.println("num1 = " + num1 + ", num2 = " + num2);
        System.out.println("Sum: " + (num1 + num2));
        System.out.println("Difference: " + (num1 - num2));
        System.out.println("Product: " + (num1 * num2));
        System.out.println("Division: " + (num1 / num2));
        System.out.println("Remainder: " + (num1 % num2));
        
        // String concatenation
        String firstName = "John";
        String lastName = "Doe";
        String fullName = firstName + " " + lastName;
        System.out.println("Full Name: " + fullName);
        
        // Constants (final keyword)
        System.out.println("\nCONSTANTS:");
        final double PI = 3.14159;
        final int DAYS_IN_WEEK = 7;
        
        System.out.println("PI = " + PI);
        System.out.println("Days in a week = " + DAYS_IN_WEEK);
        
        // Variable naming examples
        System.out.println("\n=== VARIABLE NAMING BEST PRACTICES ===");
        // Good naming conventions
        int studentAge = 20;           // camelCase
        double accountBalance = 1500.50;
        boolean isLoggedIn = true;
        String userEmail = "<EMAIL>";
        
        System.out.println("Student Age: " + studentAge);
        System.out.println("Account Balance: $" + accountBalance);
        System.out.println("Is Logged In: " + isLoggedIn);
        System.out.println("User Email: " + userEmail);
        
        System.out.println("\n🎉 Variables and Data Types Demo Complete!");
    }
}

/*
 * Key Points to Remember:
 * 1. Java is strongly typed - must declare variable type
 * 2. Use 'L' suffix for long literals
 * 3. Use 'f' suffix for float literals
 * 4. char uses single quotes, String uses double quotes
 * 5. boolean can only be true or false
 * 6. Use camelCase for variable names
 * 7. Use final keyword for constants
 */
