/**
 * Operators Example - Demonstrates all types of operators in Java
 * Author: Daily Practice
 */

public class OperatorsExample {
    public static void main(String[] args) {
        int a = 10, b = 5;
        
        System.out.println("=== Arithmetic Operators ===");
        System.out.println("a = " + a + ", b = " + b);
        System.out.println("a + b = " + (a + b));
        System.out.println("a - b = " + (a - b));
        System.out.println("a * b = " + (a * b));
        System.out.println("a / b = " + (a / b));
        System.out.println("a % b = " + (a % b));
        
        System.out.println("\n=== Relational Operators ===");
        System.out.println("a == b: " + (a == b));
        System.out.println("a != b: " + (a != b));
        System.out.println("a > b: " + (a > b));
        System.out.println("a < b: " + (a < b));
        System.out.println("a >= b: " + (a >= b));
        System.out.println("a <= b: " + (a <= b));
        
        System.out.println("\n=== Logical Operators ===");
        boolean x = true, y = false;
        System.out.println("x = " + x + ", y = " + y);
        System.out.println("x && y: " + (x && y));
        System.out.println("x || y: " + (x || y));
        System.out.println("!x: " + (!x));
        System.out.println("!y: " + (!y));
        
        System.out.println("\n=== Assignment Operators ===");
        int c = 20;
        System.out.println("Initial c = " + c);
        c += 5; // c = c + 5
        System.out.println("After c += 5: " + c);
        c -= 3; // c = c - 3
        System.out.println("After c -= 3: " + c);
        c *= 2; // c = c * 2
        System.out.println("After c *= 2: " + c);
        c /= 4; // c = c / 4
        System.out.println("After c /= 4: " + c);
        c %= 3; // c = c % 3
        System.out.println("After c %= 3: " + c);
        
        System.out.println("\n=== Increment/Decrement Operators ===");
        int d = 10;
        System.out.println("Initial d = " + d);
        System.out.println("d++ (post-increment): " + (d++));
        System.out.println("After post-increment d = " + d);
        System.out.println("++d (pre-increment): " + (++d));
        System.out.println("d-- (post-decrement): " + (d--));
        System.out.println("After post-decrement d = " + d);
        System.out.println("--d (pre-decrement): " + (--d));
        
        System.out.println("\n=== Bitwise Operators ===");
        int p = 12; // 1100 in binary
        int q = 10; // 1010 in binary
        System.out.println("p = " + p + " (binary: " + Integer.toBinaryString(p) + ")");
        System.out.println("q = " + q + " (binary: " + Integer.toBinaryString(q) + ")");
        System.out.println("p & q = " + (p & q) + " (binary: " + Integer.toBinaryString(p & q) + ")");
        System.out.println("p | q = " + (p | q) + " (binary: " + Integer.toBinaryString(p | q) + ")");
        System.out.println("p ^ q = " + (p ^ q) + " (binary: " + Integer.toBinaryString(p ^ q) + ")");
        System.out.println("~p = " + (~p));
        System.out.println("p << 1 = " + (p << 1));
        System.out.println("p >> 1 = " + (p >> 1));
        
        System.out.println("\n=== Ternary Operator ===");
        int max = (a > b) ? a : b;
        System.out.println("Maximum of " + a + " and " + b + " is: " + max);
    }
}
