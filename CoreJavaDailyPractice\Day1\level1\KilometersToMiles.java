/**
 * Level 1 Practice Program 3
 * Problem: Create a program to convert the distance of 10.8 kilometers to miles.
 *          Hint: 1 km = 1.6 miles
 * I/P => NONE
 * O/P => The distance ___ km in miles is ___
 */

public class KilometersToMiles {
    public static void main(String[] args) {
        // Given distance in kilometers
        double distanceInKm = 10.8;
        
        // Conversion factor: 1 km = 1.6 miles
        double conversionFactor = 1.6;
        
        // Convert kilometers to miles
        double distanceInMiles = distanceInKm * conversionFactor;
        
        // Display the result
        System.out.println("The distance " + distanceInKm + " km in miles is " + distanceInMiles);
        
        // Optional: Show the calculation
        System.out.println("\nCalculation:");
        System.out.println(distanceInKm + " km × " + conversionFactor + " = " + distanceInMiles + " miles");
    }
}

/*
 * Expected Output:
 * The distance 10.8 km in miles is 17.28
 * 
 * Calculation:
 * 10.8 km × 1.6 = 17.28 miles
 * 
 * Explanation:
 * - Given distance: 10.8 kilometers
 * - Conversion factor: 1 km = 1.6 miles
 * - Formula: Distance in miles = Distance in km × 1.6
 * - Result: 10.8 × 1.6 = 17.28 miles
 */
