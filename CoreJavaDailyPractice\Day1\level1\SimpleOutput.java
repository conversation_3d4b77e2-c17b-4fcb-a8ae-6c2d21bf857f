/**
 * Day 1 - Level 1 - Simple Output Practice
 * 
 * Learn different ways to print output in Java
 * This is important for seeing your program results!
 */

public class SimpleOutput {
    public static void main(String[] args) {
        // Basic printing
        System.out.println("=== Different Ways to Print ===");
        
        // println - prints and goes to next line
        System.out.println("This is line 1");
        System.out.println("This is line 2");
        System.out.println("This is line 3");
        
        System.out.println(); // Empty line for spacing
        
        // print - prints but stays on same line
        System.out.print("This ");
        System.out.print("is ");
        System.out.print("all ");
        System.out.print("on ");
        System.out.print("one ");
        System.out.println("line!");
        
        System.out.println("\n=== Printing Variables ===");
        
        // Variables to print
        String studentName = "Sarah";
        int studentGrade = 95;
        String subject = "Math";
        
        // Different ways to print variables
        System.out.println("Student name: " + studentName);
        System.out.println("Grade: " + studentGrade);
        System.out.println("Subject: " + subject);
        
        // Combining multiple variables
        System.out.println(studentName + " got " + studentGrade + " in " + subject);
        
        System.out.println("\n=== Fun with Numbers ===");
        
        int apples = 5;
        int oranges = 3;
        int totalFruit = apples + oranges;
        
        System.out.println("I have " + apples + " apples");
        System.out.println("I have " + oranges + " oranges");
        System.out.println("Total fruit: " + totalFruit);
        
        System.out.println("\n=== Creating a Simple Report ===");
        
        // Personal information
        String name = "John Doe";
        int age = 22;
        String city = "New York";
        double salary = 45000.50;
        boolean isEmployed = true;
        
        // Print a formatted report
        System.out.println("===== PERSONAL REPORT =====");
        System.out.println("Name: " + name);
        System.out.println("Age: " + age + " years old");
        System.out.println("City: " + city);
        System.out.println("Salary: $" + salary);
        System.out.println("Employed: " + isEmployed);
        System.out.println("============================");
        
        System.out.println("\n=== Special Characters ===");
        
        // Using escape characters
        System.out.println("This has a tab:\tSee the space?");
        System.out.println("This has a quote: \"Hello World\"");
        System.out.println("This has a backslash: \\");
        
        System.out.println("\n🌟 Excellent work with output!");
        System.out.println("You now know how to display information!");
    }
}

/*
 * What you learned:
 * 1. System.out.println() - prints and goes to new line
 * 2. System.out.print() - prints but stays on same line
 * 3. How to print variables with text using +
 * 4. How to combine multiple variables in one print
 * 5. How to create formatted output
 * 6. Special characters like \t (tab) and \" (quote)
 * 
 * Practice Ideas:
 * - Create your own personal report
 * - Print a simple menu for a restaurant
 * - Make a shopping list with prices
 * - Create a student grade report
 */
